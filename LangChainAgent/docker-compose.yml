services:
  banco_de_dados:
    container_name: postgres
    image: postgres:16-alpine
    user: "70:70"  # Explicitly set UID:GID
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - ./volumes/postgres:/var/lib/postgresql/data
    ports:
      - '127.0.0.1:5432:5432'

  redis:
    image: redis:7.2
    container_name: redis
    ports:
      - "127.0.0.1:6379:6379" 
    volumes:
      - ./volumes/redis:/data
