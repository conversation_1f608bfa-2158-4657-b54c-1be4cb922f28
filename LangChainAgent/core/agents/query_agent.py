import json
import re
from typing import Dict, Any, List
from .base_agent import Base<PERSON>gent
from core.utils.config_loader import Config<PERSON>oader
from datetime import datetime
import os

from .specialist_agent import SpecialistAgent
from ..utils.llm_manager import LlmManager


class QueryAgent(BaseAgent):
    def __init__(self, config: Dict[str, Any], llm, memory_manager=None, data_path: str = "../../data/"):
        super().__init__(config, llm, memory_manager=memory_manager)
        self.data_path = data_path
        self._llm_manager = LlmManager(config)

    def analyze_query(self, query: str,
                      customer_id: str,
                      memory_manager,
                      specialists_agents: list[SpecialistAgent]) -> Dict[str, Any]:
        """
        Analyze the query using LLM to determine routing and specialized queries.
        """
        print(f"\n[QueryAgent] Analyzing query: {query}")
<<<<<<< HEAD

        # Get memory context
        short_term_memory = memory_manager.get_short_term_memory(customer_id)
        long_term_memory = memory_manager.get_long_term_memory(customer_id)

        # --- ENRICHED CONTEXT ---
        conversation_history = short_term_memory.get('conversation', {}).get('conversation_history', [])
        all_products_discussed = short_term_memory.get('conversation', {}).get('all_products_discussed', [])
        last_product_list = short_term_memory.get('conversation', {}).get('last_product_list', [])
        products_of_interest = short_term_memory.get('conversation', {}).get('products_of_interest', [])

        # Detect possible category switch
        def detect_category_switch(query, last_product_list):
            categories = set(
                [p.get('product', {}).get('category', '').lower() for p in last_product_list if p.get('product')])
            for cat in categories:
                if cat and cat not in query.lower():
                    # If query mentions a new category not in last list, treat as switch
                    if any(word in query.lower() for word in
                           ['notebook', 'smartphone', 'fone', 'tablet', 'tv', 'monitor']):
                        return True
            return False

        category_switch = detect_category_switch(query, last_product_list)
        if category_switch:
            # Filter STM context to only products of the new category
            new_category = None
            for word in ['notebook', 'smartphone', 'fone', 'tablet', 'tv', 'monitor']:
                if word in query.lower():
                    new_category = word
                    break
            if new_category:
                last_product_list = [p for p in last_product_list if
                                     p.get('product', {}).get('category', '').lower() == new_category]
                products_of_interest = [p for p in products_of_interest if
                                        p.get('product', {}).get('category', '').lower() == new_category]

        # Prepare memory context for analysis
        memory_context = self._build_memory_context(customer_id, memory_manager, short_term_memory, long_term_memory,
                                                    conversation_history=conversation_history,
                                                    all_products_discussed=all_products_discussed,
                                                    last_product_list=last_product_list,
                                                    products_in_focus=short_term_memory.get('products_in_focus'))

        # Build the analysis prompt
        prompt = f""" Você é um agente especializado em análise de consultas de clientes. Sua função é analisar a pergunta do cliente, 
                    o contexto da conversa (memória de curto e longo prazo) e determinar o roteamento de agentes a serem utilizados.
=======
        
        # Build memory context
        memory_context = self._build_memory_context(customer_id, memory_manager)
        
        # Build and execute LLM prompt
        prompt = self._build_analysis_prompt(query, memory_context)
        
        # Log prompt for debugging
        self._log_prompt(prompt, query, customer_id)
        
        # Get LLM response
        response = self._call_llm(prompt, customer_id=customer_id)
        
        # Log response
        self._log_response(response, customer_id)
        
        # Parse and validate response
        analysis = self._parse_and_validate_response(response, query)
        
        # Inject seller info if needed
        if analysis.get('task_type') == 'store_info':
            analysis = self._inject_seller_info(analysis)
        
        # Enhance with product context
        analysis = self._enhance_product_context(analysis, memory_manager.get_short_term_memory(customer_id))
        
        print(f"[QueryAgent] Final analysis: {analysis}")
        return analysis

    def _build_memory_context(self, customer_id: str, memory_manager) -> str:
        """Build comprehensive memory context for query analysis."""
        stm = memory_manager.get_short_term_memory(customer_id)
        ltm = memory_manager.get_long_term_memory(customer_id)
        
        context_parts = []
        
        # Short-term memory
        if stm:
            context_parts.append("MEMÓRIA DE CURTO PRAZO:")
            context_parts.append(f"- Última query: {stm.get('query', 'N/A')}")
            context_parts.append(f"- Task type anterior: {stm.get('task_type', 'N/A')}")
            
            # Conversation context
            conversation = stm.get('conversation', {})
            if conversation.get('last_product_list'):
                context_parts.append("- Últimos produtos listados:")
                for prod in conversation['last_product_list'][:3]:  # Show top 3
                    p = prod.get('product', {})
                    context_parts.append(f"  • {p.get('name', 'N/A')} (ID: {p.get('id', 'N/A')})")
            
            if conversation.get('products_in_focus'):
                context_parts.append("- Produtos em foco:")
                for prod in conversation['products_in_focus'][:3]:
                    p = prod.get('product', prod)
                    context_parts.append(f"  • {p.get('name', 'N/A')} (ID: {p.get('id', 'N/A')})")

        # Long-term memory
        if ltm:
            context_parts.append("\nMEMÓRIA DE LONGO PRAZO:")
            context_parts.append(f"- Preferências: {ltm.get('preferences', [])}")
            context_parts.append(f"- Histórico de compras: {ltm.get('purchase_history', [])}")

        return '\n'.join(context_parts) if context_parts else "Sem contexto de memória disponível."

    def _build_analysis_prompt(self, query: str, memory_context: str) -> str:
        """Build the LLM analysis prompt."""
        return f"""Você é um agente especializado em análise de consultas de clientes. Sua função é analisar a pergunta do cliente, 
                    o contexto da conversa (memória de curto e longo prazo) e determinar o roteamento de agentes a serem utilizados, 
                    bem como as queries especializadas para cada agente.

                    Sua tarefa é responder com um JSON contendo as seguintes informações:
                    - task_type: Tipo de tarefa (product_query, store_info, general, etc.)
                    - is_follow_up: Se a consulta é um follow-up (true/false)
                    - end_of_conversation: Se a consulta indica o fim da conversa (true/false)
                    - required_agents: Lista de agentes necessários para atender à consulta
                    - intention: A intenção principal da consulta 
                    - entities: Entidades extraídas na consulta (categoria, marca, atributos, modelo, preços, etc.)
                    - reasoning: Justificativa para o roteamento e especialização das queries
                    - context_notes: Resumo do contexto relevante para os agentes
                    - specialized_queries: Queries especializadas para cada agente
                    - product_context: Contexto do produto relevante (ID, nome, nível de detalhe)
                    - relevant_products: Lista de produtos relevantes mencionados na consulta
>>>>>>> 27ec5066 (updated query agent)

                    CONTEXTO: {memory_context}
                    QUERY: {query}

<<<<<<< HEAD
                    REGRAS DE ROTEAMENTO:
                    1. FOLLOW-UP (product_specialist + response_generator):
                    - Referências a produtos já mencionados ("o primeiro", "ele", "esse")
                    - Características de produtos já discutidos
                    - Processos de suporte gerais ("como trocar", "qual garantia")
                    - product_specialist deve retornar as informações detalhadas do produto em questão

                    2. NOVA CONSULTA (product_specialist + response_generator):
                    - Busca por novos produtos
                    - Problemas com produtos específicos não identificados
                    - Decisões de compra ("vou comprar", "decidido")

                    3. ENCERRAMENTO (apenas response_generator):
                    - Gratidão: "obrigado", "valeu"
                    - Despedidas: "tchau", "até logo"
                    - Satisfação: "perfeito", "ótimo"

                    SEMPRE inclua response_generator.

                    IMPORTANTE: O campo "context_notes" DEVE SEMPRE estar presente no JSON de resposta. 
                    Ele deve resumir o contexto relevante (memória, preferências, histórico, etc.) para os agentes especializados. 
                    Se não houver contexto relevante, escreva "Sem contexto relevante".
                    Sempre adicione a última query do usuário no campo "context_notes".

                    MUITO IMPORTANTE: Se a pergunta do cliente for vaga ou ambígua, use o contexto da sessão (última query do usuário, última lista de produtos, produtos de interesse, histórico da conversa) para inferir a intenção e os produtos relevantes.
                    SEMPRE gere a specialized_query para o product_specialist de forma específica e inequívoca, referenciando explicitamente o(s) produto(s) correto(s) pelo nome ou ID, nunca de forma vaga.

                    CONTEXTO DE FOLLOW-UP: Para perguntas de follow-up que se referem a "produtos" ou produtos mencionados anteriormente, extraia TODOS os produtos do histórico da conversa e inclua seus IDs na specialized_query.

                    SPECIALIZED QUERIES ESPECÍFICAS - ADAPTE BASEADO NA PERGUNTA:
                    - Para perguntas sobre PREÇOS:
                      * product_specialist: "Obter preços detalhados dos produtos disponíveis"
                      * response_generator: "Listar produtos com preços para o cliente"
                    - Para CUSTO-BENEFÍCIO:
                      * product_specialist: "Obter especificações completas e preços dos produtos"
                      * response_generator: "Analisar custo-benefício dos produtos e recomendar o melhor"
                    - Para COMPARAÇÕES:
                      * product_specialist: "Comparar especificações e preços dos produtos: [IDs específicos]"
                      * response_generator: "Comparar produtos e destacar diferenças"
                    - Para RECOMENDAÇÕES:
                      * product_specialist: "Analisar produtos para recomendar melhor opção"
                      * response_generator: "Recomendar tablet baseado em: [critério específico]"
                    - Para ESPECIFICAÇÕES:
                      * product_specialist: "Obter especificações detalhadas do produto: [ID específico]"
                      * response_generator: "Apresentar especificações técnicas detalhadas"

                    Nunca devolva sua cadeia de pensamentos, apenas a justificativa concisa no campo "reasoning". Não gere os campos como <think> ou <think> </think>, ou <reasoning> ou <reasoning> </reasoning>.

<<<<<<< HEAD
                    FUNDAMENTAL: Extraia e liste TODOS os nomes ou IDs de produtos mencionados na query do cliente, mesmo que sejam produtos novos e não estejam presentes na memória ou contexto anterior. Sempre preencha o campo "relevant_products" com uma lista de todos os produtos referenciados na query, sejam eles novos ou já discutidos. Se o produto não estiver no contexto, inclua pelo menos o nome (e qualquer informação disponível) no objeto correspondente em "relevant_products".

                    RESPONDA APENAS COM JSON VÁLIDO - SEM EXPLICAÇÕES OU MARKDOWN:
=======
                    IMPORTANTE:                       
                         - Nunca devolva sua cadeia de pensamentos, apenas a justificativa concisa no campo "reasoning". 
                         
                    MODELO DE RESPOSTA - RESPONDA APENAS COM JSON VÁLIDO - SEM EXPLICAÇÕES OU MARKDOWN:
>>>>>>> 21f7b0b1 (refactored products agent)
=======
                    REGRAS:
                    1. SEMPRE responda com JSON válido.
                    2. SEMPRE inclua o response_generator no campo "required_agents".
                    3. SEMPRE DETECTE se a consulta é um follow-up ou nova consulta.
                    4. SEMPRE retorne product_specialist se a consulta mencionar produtos.
                    5. SEMPRE inclua o campo "context_notes" no JSON de resposta.
                    6. SEMPRE gere a specialized_query para o product_specialist de forma específica.

                    INTENTION: listar_produtos, consultar_preco, filtrar_por_categoria, filtrar_por_ID, filtrar_por_marca, 
                    filtrar_por_preco, comparar_produtos, suporte_pos_venda, mostrar_mais_vendido, recomendacao, encerramento, outra

                    MODELO DE RESPOSTA - RESPONDA APENAS COM JSON VÁLIDO:
>>>>>>> 27ec5066 (updated query agent)
                    {{
                    "task_type": "product_query",
                    "is_follow_up": false,
                    "end_of_conversation": false,
                    "required_agents": ["response_generator", "product_specialist"],
                    "reasoning": "Justificativa concisa",
<<<<<<< HEAD
                    "context_notes": "Resumo do contexto relevante para os agentes. Sempre presente.",
=======
                    "context_notes": "Resumo do contexto relevante",
                    "intention": "listar_produtos",
                    "entities": {{
                        "categoria": "notebook",
                        "marca": "Dell",
                        "atributos": {{"RAM": "16GB", "SSD": "512GB"}},
                        "preco_max": 4000,
                        "preco_min": null
                    }},
>>>>>>> 27ec5066 (updated query agent)
                    "specialized_queries": {{
                        "product_specialist": "Buscar notebooks Dell com 16GB RAM e SSD 512GB até R$ 4000 (detail_level: detailed)",
                        "response_generator": "Analisar e recomendar melhor notebook Dell"
                    }},
                    "product_context": {{
                        "detail_level": "detailed",
                        "search_type": "filtered"
                    }},
                    "relevant_products": []
                    }}"""

<<<<<<< HEAD
                    IMPORTANTE: Use null para valores vazios, não "Nenhum", "N/A" ou "None".
                    Use true/false para booleanos, não True/False.
                    RESPONDA APENAS COM O JSON - SEM TEXTO ADICIONAL."""

        # Before calling the LLM, log the prompt
        if not customer_id:
            print("[QueryAgent] WARNING: customer_id not set for logging, using 'unknown_customer'.")
            customer_id = "unknown_customer"
        log_dir = os.path.join(os.path.dirname(__file__), f'../../logs/{customer_id}')
        os.makedirs(log_dir, exist_ok=True)
        log_path = os.path.join(log_dir, 'query_agent_prompts.log')
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(
                f"\n---\n[{datetime.now().isoformat()}] customer_id={customer_id} | query={query}\nPROMPT:\n{prompt}\n")
        print(f"[QueryAgent] Calling LLM for analysis...")
        response = self._call_llm(prompt, customer_id=customer_id)
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(f"RESPONSE:\n{response}\n")

        # Parse the JSON response with improved error handling
        analysis = self._llm_manager._parse_llm_response(response, short_term_memory)
        seller_info_needed = False
        if analysis:
            print(f"[QueryAgent] Parsed analysis: {analysis}")
            # Use LLM's classification to decide if seller info is needed
            if analysis.get('task_type') == 'store_info':
                seller_info_needed = True
        else:
            print(f"[QueryAgent] JSON parsing failed, using fallback analysis")
            analysis = self._fallback_analysis(query, short_term_memory)
            # Use fallback keyword detection for store info
            if analysis.get('task_type') == 'store_info':
                seller_info_needed = True
        # Inject seller info into context if needed
        if seller_info_needed:
            try:
                seller_json_path = os.path.join(self.data_path, "seller.json")
                seller_info = ConfigLoader.get_seller_json(seller_json_path)

                # Add seller_info as a separate field for orchestrator
                analysis['seller_info'] = seller_info

                # Also add to context_notes for backward compatibility
                if 'context_notes' in analysis:
                    analysis['context_notes'] += f"\n\nINFORMAÇÕES DA LOJA DISPONÍVEIS"
                else:
                    analysis['context_notes'] = "INFORMAÇÕES DA LOJA DISPONÍVEIS"

                print(f"[QueryAgent] Loaded seller info: {seller_info.get('name', 'Unknown')}")
            except Exception as e:
                print(f"[QueryAgent] Could not load seller info: {e}")
        # --- RELEVANT PRODUCTS RESOLUTION ---
        relevant_products = []
        clarification_needed = False
        clarification_question = None
        # Try to resolve by ordinal (primeiro, segundo, terceiro)
        ordinal_map = {'primeiro': 0, 'segundo': 1, 'terceiro': 2}
        found_ordinal = False
        for word, idx in ordinal_map.items():
            if word in query.lower() and last_product_list and len(last_product_list) > idx:
                relevant_products = [last_product_list[idx]]
                found_ordinal = True
                break
        # Try to resolve by name or ID if not ordinal
        if not found_ordinal:
            for prod in (last_product_list or products_of_interest):
                p = prod.get('product', {})
                if p.get('name', '').lower() in query.lower() or p.get('id', '') in query:
                    relevant_products.append(prod)
        # Detect ambiguous/multi-product queries
        ambiguous_terms = ['quais', 'quais?', 'eles', 'esses', 'todos', 'ambos', 'as duas', 'as dois', 'as três',
                           'as tres', 'as quatro', 'as quatro', 'as cinco', 'as cinco', 'as seis', 'as seis', 'as sete',
                           'as sete', 'as oito', 'as oito', 'as nove', 'as nove', 'as dez', 'as dez']
        is_ambiguous = any(term in query.lower() for term in ambiguous_terms)
        # If nothing found, fallback to all products_of_interest for broad context
        if not relevant_products and (is_ambiguous or '?' in query):
            relevant_products = products_of_interest
        # If still ambiguous, set clarification flag
        if is_ambiguous and not relevant_products:
            clarification_needed = True
            clarification_question = "Sua pergunta está um pouco vaga. Você pode especificar a qual produto ou categoria está se referindo?"
        # If multi-product query (e.g., sum, list all), generate explicit queries for each
        multi_product_terms = ['soma', 'total', 'todos', 'quais', 'lista', 'listar']
        is_multi = any(term in query.lower() for term in multi_product_terms)
        # Inject clarification flags
        if clarification_needed:
            analysis['clarification_needed'] = True
            analysis['clarification_question'] = clarification_question
        analysis['relevant_products'] = relevant_products

        # Debug: Check if seller_info is in analysis before returning
        if 'seller_info' in analysis:
            print(f"[QueryAgent] Returning analysis WITH seller_info: {analysis['seller_info'].get('name', 'Unknown')}")
        else:
            print(f"[QueryAgent] Returning analysis WITHOUT seller_info")
        print(f"[QueryAgent] Final analysis keys: {list(analysis.keys())}")

        return analysis

    def _build_memory_context(self, customer_id: str, memory_manager, stm, ltm, conversation_history=None,
                              all_products_discussed=None, last_product_list=None, products_in_focus=None) -> str:
        """Build comprehensive memory context for query analysis, using new context fields."""
        context_parts = []

        if stm:
            context_parts.append("MEMÓRIA DE CURTO PRAZO:")
            context_parts.append(f"- Última query: {stm.get('query', 'N/A')}")
            context_parts.append(f"- Task type anterior: {stm.get('task_type', 'N/A')}")

            # Add specific product context for follow-ups - check multiple possible locations
            product_info = stm.get('product_info') or stm.get('results') or stm.get('products') or stm.get(
                'conversation', {}).get('product_info')
            if product_info:
                if isinstance(product_info, list) and len(product_info) > 0:
                    context_parts.append("- Produtos mencionados anteriormente:")
                    for i, product_data in enumerate(product_info[:3], 1):  # Show first 3 products
                        if isinstance(product_data, dict):
                            # Handle both full product structure and simplified structure
                            if 'product' in product_data:
                                product = product_data['product']
                            else:
                                product = product_data  # Simplified structure

                            context_parts.append(
                                f"  {i}. {product.get('name', 'N/A')} (ID: {product.get('id', 'N/A')}) - R$ {product.get('price', 'N/A')} - {product.get('category', 'N/A')}")
                            # Add more details for better context
                            if product.get('description'):
                                context_parts.append(f"     Descrição: {product.get('description', '')[:50]}...")
                else:
                    context_parts.append("- Produtos mencionados anteriormente: Sim")
            else:
                context_parts.append("- Produtos mencionados anteriormente: Não")
        else:
            context_parts.append("CONTEXTO: Primeira interação com o cliente")

        # Enhanced LTM context with conversation history
        if ltm:
            context_parts.append("MEMÓRIA DE LONGO PRAZO:")

            # Get seller configuration for context retrieval using enhanced ConfigLoader
            try:
                summary_settings = ConfigLoader.get_summary_settings()

                # Get conversation context based on seller configuration
                conversation_context = memory_manager.get_conversation_context(customer_id, summary_settings)

                if conversation_context:
                    context_parts.append("- Histórico de conversas recentes:")
                    for i, interaction in enumerate(conversation_context, 1):
                        interaction_type = interaction.get('interaction_type', 'unknown')
                        summary = interaction.get('summary', 'N/A')
                        timestamp = interaction.get('timestamp', 'N/A')

                        # Format timestamp for readability
                        try:
                            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                            formatted_time = dt.strftime("%d/%m/%Y %H:%M")
                        except:
                            formatted_time = timestamp

                        context_parts.append(f"  {i}. [{formatted_time}] {interaction_type}: {summary[:100]}...")

                        # Add product information if available
                        products = interaction.get('products_discussed', [])
                        if products:
                            product_names = [p.get('name', 'N/A') for p in products if isinstance(p, dict)]
                            if product_names:
                                context_parts.append(f"     Produtos: {', '.join(product_names)}")

                # Get relationship context
                relationship_context = memory_manager.get_customer_relationship_context(customer_id, summary_settings)
                if relationship_context:
                    context_parts.append("- Contexto do relacionamento:")
                    context_parts.append(f"  Status: {relationship_context.get('relationship_status', 'unknown')}")
                    context_parts.append(f"  Total de interações: {relationship_context.get('total_interactions', 0)}")

                    preferences = relationship_context.get('preferences', [])
                    if preferences:
                        context_parts.append(f"  Preferências: {', '.join(preferences[:3])}")  # Show top 3

                    common_intents = relationship_context.get('common_intents', [])
                    if common_intents:
                        context_parts.append(f"  Intenções comuns: {', '.join(common_intents)}")

            except Exception as e:
                print(f"[QueryAgent] Error loading seller config for context: {e}")
                # Fallback to basic LTM display
                for i, entry in enumerate(ltm[-3:], 1):  # Show last 3 entries
                    context_parts.append(f"- Entrada {i}: {entry}")
        else:
            context_parts.append("HISTÓRICO: Cliente novo, sem interações anteriores")

        # Add conversation history
        if conversation_history:
            context_parts.append("HISTÓRICO DA CONVERSA (últimos 10 turnos):")
            for turn in conversation_history[-10:]:
                context_parts.append(
                    f"  [{turn.get('turn_id')}] Usuário: {turn.get('user_query')} | Bot: {turn.get('agent_response')}")
        # Add products in focus for this turn
        if products_in_focus:
            context_parts.append("PRODUTOS EM FOCO PARA ESTA CONSULTA:")
            for prod in products_in_focus:
                if 'product' in prod:
                    p = prod['product']
                else:
                    p = prod
                context_parts.append(f"  - {p.get('name', 'N/A')} (ID: {p.get('id', 'N/A')})")
        # Add all products discussed
        if all_products_discussed:
            context_parts.append("TODOS OS PRODUTOS DISCUTIDOS NA SESSÃO:")
            for prod in all_products_discussed:
                if 'product' in prod:
                    p = prod['product']
                else:
                    p = prod
                context_parts.append(f"  - {p.get('name', 'N/A')} (ID: {p.get('id', 'N/A')})")
        # Add last product list
        if last_product_list:
            context_parts.append("ÚLTIMA LISTA DE PRODUTOS APRESENTADA:")
            for i, prod in enumerate(last_product_list, 1):
                p = prod.get('product', {})
                context_parts.append(f"  {i}. {p.get('name', 'N/A')} (ID: {p.get('id', 'N/A')})")

        return "\n".join(context_parts)

    def _fallback_analysis(self, query: str, short_term_memory: Dict) -> Dict[str, Any]:
        """Fallback analysis when LLM parsing fails. This method expects that, in normal operation, the LLM will extract all product names/IDs mentioned in the query (even if new), and populate relevant_products accordingly. Here, fallback logic only attempts to match products from context."""
        query_lower = query.lower()

        # Enhanced keyword detection
        support_keywords = ['problema', 'ajuda', 'suporte', 'erro', 'defeito', 'garantia',
                            'troca', 'devolução', 'defeituoso', 'quebrado', 'não funciona',
                            'mau funcionamento', 'assistência']
        product_keywords = ['produto', 'preço', 'estoque', 'disponível', 'comprar',
                            'smartphone', 'notebook', 'fone', 'valor', 'especificação',
                            'produtos', 'item', 'artigo', 'mercadoria', 'bem', 'detalhes',
                            'tablet', 'tablets', 'ipad', 'monitor', 'monitores', 'console',
                            'consoles', 'mouse', 'teclado', 'headset', 'fones', 'cabo',
                            'cabos', 'carregador', 'carregadores', 'processador', 'placa',
                            'memória', 'ssd', 'hd', 'webcam', 'camera', 'microfone',
                            'custo', 'benefício', 'custo-benefício', 'barato', 'caro',
                            'melhor', 'recomenda', 'indica', 'valores', 'preços', 'quanto']
        store_keywords = ['horário', 'endereço', 'contato', 'telefone', 'loja',
                          'funcionamento', 'localização', 'aberto', 'fechado']
        general_keywords = ['obrigado', 'valeu', 'agradeço', 'tchau', 'até logo']

        # Determine task type based on keywords
        if any(keyword in query_lower for keyword in support_keywords):
            task_type = 'support'
            reasoning = 'Fallback: Palavras-chave de suporte detectadas'
        elif any(keyword in query_lower for keyword in product_keywords):
=======
    def _parse_and_validate_response(self, response: str, query: str) -> Dict[str, Any]:
        """Parse LLM response with robust error handling."""
        try:
            # Clean response
            cleaned_response = response.strip()
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response.replace('```json', '').replace('```', '').strip()
            elif cleaned_response.startswith('```'):
                cleaned_response = cleaned_response.replace('```', '').strip()
            
            # Extract JSON
            json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
            if json_match:
                cleaned_response = json_match.group()
            
            analysis = json.loads(cleaned_response)
            
            # Validate required fields
            required_fields = ['task_type', 'is_follow_up', 'required_agents', 'reasoning']
            for field in required_fields:
                if field not in analysis:
                    print(f"[QueryAgent] Missing required field: {field}, using fallback")
                    return self._fallback_analysis(query)
            
            # Ensure context_notes exists
            if 'context_notes' not in analysis:
                analysis['context_notes'] = 'Sem contexto relevante'
            
            return analysis
            
        except (json.JSONDecodeError, Exception) as e:
            print(f"[QueryAgent] Error parsing JSON: {e}, using fallback")
            return self._fallback_analysis(query)

    def _fallback_analysis(self, query: str) -> Dict[str, Any]:
        """Simple fallback analysis when LLM parsing fails."""
        query_lower = query.lower()
        
        # Determine task type
        if any(word in query_lower for word in ['produto', 'preço', 'notebook', 'smartphone', 'tablet']):
>>>>>>> 27ec5066 (updated query agent)
            task_type = 'product_query'
            required_agents = ['product_specialist', 'response_generator']
        elif any(word in query_lower for word in ['loja', 'endereço', 'horário', 'contato']):
            task_type = 'store_info'
<<<<<<< HEAD
            reasoning = 'Fallback: Palavras-chave de informação da loja detectadas'
        elif any(keyword in query_lower for keyword in general_keywords):
            task_type = 'general'
            reasoning = 'Fallback: Palavras-chave gerais detectadas'
        else:
            task_type = 'general'
            reasoning = 'Fallback: Nenhuma palavra-chave específica detectada'

        # Determine if it's a follow-up query
        follow_up_indicators = ['mais', 'detalhes', 'outro', 'similar', 'parecido',
                                'também', 'primeiro', 'segundo', 'próximo', 'ele', 'ela',
                                'este', 'esta', 'esse', 'essa', 'tem', 'possui', 'inclui']
        is_follow_up = any(indicator in query_lower for indicator in follow_up_indicators)

        # Determine if it's the end of conversation
        end_conversation_indicators = [
            'obrigado', 'valeu', 'agradeço', 'muito obrigado', 'obrigada',
            'tchau', 'até logo', 'até mais', 'adeus', 'até a próxima',
            'perfeito', 'entendi', 'ótimo', 'excelente', 'maravilha',
            'vou comprar', 'decidido', 'feito', 'beleza', 'tudo certo',
            'entendi tudo', 'tudo bem', 'ok', 'certo', 'blz'
        ]
        end_of_conversation = any(indicator in query_lower for indicator in end_conversation_indicators)

        # Determine required agents based on task type and follow-up status
        if task_type == 'product_query':
            # Check if this is a follow-up about existing products
            has_previous_products = short_term_memory and short_term_memory.get('product_info')

            if is_follow_up and has_previous_products:
                # Follow-up about existing products - only use response_generator
                required_agents = ['response_generator']
                reasoning += ' | Follow-up sobre produtos existentes - usando apenas response_generator'
            else:
                # New product query - use both agents
                required_agents = ['product_specialist', 'response_generator']
                reasoning += ' | Nova consulta de produto - usando product_specialist + response_generator'
        elif task_type == 'support':
            # Enhanced support classification logic
            # Check if it's a process-related support query (only response_generator needed)
            process_support_keywords = ['como faço', 'quero reclamar', 'qual a garantia', 'processo', 'troca',
                                        'devolução']
            is_process_support = any(keyword in query_lower for keyword in process_support_keywords)

            if is_process_support:
                # Process support - only response_generator needed
                required_agents = ['response_generator']
                reasoning += ' | Processo de suporte - usando apenas response_generator'
            else:
                # Product-specific problem - might need product_specialist to identify the product
                required_agents = ['product_specialist', 'response_generator']
                reasoning += ' | Problema específico de produto - usando product_specialist + response_generator'
        else:
            # Non-product queries always use only response_generator
            required_agents = ['response_generator']
            reasoning += ' | Consulta não relacionada a produtos - usando apenas response_generator'

        # Special handling for purchase decisions - they are new queries, not follow-ups
        purchase_decision_keywords = ['vou comprar', 'decidido', 'feito', 'perfeito']
        if any(keyword in query_lower for keyword in purchase_decision_keywords):
            is_follow_up = False  # Override follow-up detection for purchase decisions
            reasoning += ' | Decisão de compra detectada - tratada como nova consulta'

        # Generate specialized queries based on task type and context
        specialized_queries = self._generate_specialized_queries(query, task_type, is_follow_up, short_term_memory,
                                                                 required_agents)

        # Generate product context
        product_context = self._generate_product_context(query, is_follow_up, short_term_memory)

=======
            required_agents = ['response_generator']
        else:
            task_type = 'general'
            required_agents = ['response_generator']
        
        # Determine if follow-up
        follow_up_indicators = ['mais', 'detalhes', 'outro', 'ele', 'ela', 'este', 'essa']
        is_follow_up = any(indicator in query_lower for indicator in follow_up_indicators)
        
>>>>>>> 27ec5066 (updated query agent)
        return {
            'task_type': task_type,
            'is_follow_up': is_follow_up,
            'end_of_conversation': False,
            'required_agents': required_agents,
            'reasoning': 'Análise de fallback baseada em palavras-chave',
            'context_notes': 'Análise simplificada',
            'specialized_queries': {
                'product_specialist': f"Buscar produtos para: {query} (detail_level: basic)",
                'response_generator': f"Gerar resposta para: {query}"
            },
            'product_context': {'detail_level': 'basic'},
            'relevant_products': []
        }

<<<<<<< HEAD
    def _generate_specialized_queries(self, query: str, task_type: str, is_follow_up: bool, short_term_memory: Dict,
                                      required_agents: list = None) -> Dict[str, str]:
        specialized_queries = {}
        if task_type == 'product_query':
            # Determine detail level based on query intent
            detail_level = 'basic'
            if any(word in query.lower() for word in ['detalhes', 'especificações', 'características', 'completo', 'tudo sobre']):
                detail_level = 'detailed'
            elif any(word in query.lower() for word in ['comparar', 'diferença', 'melhor', 'versus']):
                detail_level = 'comparison'
            
            if is_follow_up:
                product_info = short_term_memory.get('conversation', {}).get('product_info', [])
                if product_info and isinstance(product_info, list) and len(product_info) > 0:
                    product_name = product_info[0].get('product', {}).get('name', '')
<<<<<<< HEAD
                    specialized_queries[
                        'product_specialist'] = f"Buscar detalhes do produto '{product_name}' para: {query}"
=======
                    specialized_queries['product_specialist'] = f"Buscar detalhes do produto '{product_name}' para: {query} (detail_level: {detail_level})"
>>>>>>> 21f7b0b1 (refactored products agent)
                else:
                    specialized_queries['product_specialist'] = f"Buscar detalhes específicos para: {query} (detail_level: {detail_level})"
            else:
                specialized_queries['product_specialist'] = f"Buscar produtos para: {query} (detail_level: {detail_level})"
        elif task_type == 'support':
            specialized_queries['response_generator'] = f"Gerar resposta de suporte para: {query}"
        elif task_type == 'store_info':
            specialized_queries['response_generator'] = f"Gerar informação da loja para: {query}"
        else:
            specialized_queries['response_generator'] = f"Gerar resposta geral para: {query}"
        if required_agents:
            for agent_name in required_agents:
                if agent_name not in specialized_queries:
                    if agent_name == 'product_specialist':
                        specialized_queries[agent_name] = f"Gerar consulta de produto para: {query}"
                    elif agent_name == 'response_generator':
                        specialized_queries[agent_name] = f"Gerar resposta para: {query}"
                    else:
                        specialized_queries[agent_name] = f"Gerar ação para: {query}"
        # Use last_product_list and all_products_discussed to resolve references like 'the second smartphone'
        last_product_list = short_term_memory.get('conversation', {}).get('last_product_list', [])
        # If follow-up and query refers to ordinal (e.g., 'segundo', 'terceiro'), try to resolve
        ordinal_map = {'primeiro': 0, 'segundo': 1, 'terceiro': 2}
        for word, idx in ordinal_map.items():
            if word in query.lower() and last_product_list and len(last_product_list) > idx:
                product_name = last_product_list[idx].get('product', {}).get('name', '')
                if product_name:
                    if 'product_specialist' in required_agents:
                        specialized_queries[
                            'product_specialist'] = f"Buscar detalhes do produto '{product_name}' para: {query}"
                        specialized_queries[
                            'response_generator'] = f"Gerar resposta detalhada para follow-up sobre '{product_name}': {query}"
        return specialized_queries

    def _generate_product_context(self, query: str, is_follow_up: bool, short_term_memory: Dict) -> Dict[str, str]:
        product_context = {
            "product_id": None,
            "product_name": None,
            "detail_level": "basic"
        }
        if is_follow_up:
            conversation = short_term_memory.get("conversation", {})
            product_info = conversation.get("product_info", [])
            if product_info and isinstance(product_info, list) and len(product_info) > 0:
                first_product = product_info[0].get('product', {})
                product_context["product_id"] = first_product.get('id')
                product_context["product_name"] = first_product.get('name')
                product_context["detail_level"] = "detailed"
        return product_context

=======
    def _inject_seller_info(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Inject seller information when needed."""
        try:
            seller_json_path = os.path.join(self.data_path, "seller.json")
            if os.path.exists(seller_json_path):
                with open(seller_json_path, 'r', encoding='utf-8') as f:
                    seller_info = json.load(f)
                    analysis['seller_info'] = seller_info
                    print(f"[QueryAgent] Injected seller info: {seller_info.get('name', 'Unknown')}")
        except Exception as e:
            print(f"[QueryAgent] Error loading seller info: {e}")
        
        return analysis

    def _enhance_product_context(self, analysis: Dict[str, Any], stm: Dict) -> Dict[str, Any]:
        """Enhance analysis with product context from memory."""
        if not stm or analysis.get('task_type') != 'product_query':
            return analysis
        
        # Add relevant products from memory if follow-up
        if analysis.get('is_follow_up'):
            conversation = stm.get('conversation', {})
            last_products = conversation.get('last_product_list', [])
            
            relevant_products = []
            for prod in last_products[:5]:  # Limit to 5 most recent
                p = prod.get('product', {})
                if p.get('id') and p.get('name'):
                    relevant_products.append({
                        'product_id': p.get('id'),
                        'product_name': p.get('name')
                    })
            
            if relevant_products:
                analysis['relevant_products'] = relevant_products
                
                # Enhance specialized query with product IDs
                if 'product_specialist' in analysis.get('specialized_queries', {}):
                    ids_str = ', '.join([p['product_id'] for p in relevant_products])
                    current_query = analysis['specialized_queries']['product_specialist']
                    analysis['specialized_queries']['product_specialist'] = f"{current_query} - IDs: {ids_str}"
        
        return analysis

    def _log_prompt(self, prompt: str, query: str, customer_id: str):
        """Log prompt for debugging."""
        if not customer_id:
            customer_id = "unknown_customer"
        
        log_dir = os.path.join(os.path.dirname(__file__), f'../../logs/{customer_id}')
        os.makedirs(log_dir, exist_ok=True)
        log_path = os.path.join(log_dir, 'query_agent_prompts.log')
        
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(f"\n---\n[{datetime.now().isoformat()}] customer_id={customer_id} | query={query}\nPROMPT:\n{prompt}\n")

    def _log_response(self, response: str, customer_id: str):
        """Log LLM response for debugging."""
        log_dir = os.path.join(os.path.dirname(__file__), f'../../logs/{customer_id}')
        log_path = os.path.join(log_dir, 'query_agent_prompts.log')
        
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(f"RESPONSE:\n{response}\n")
    
>>>>>>> 27ec5066 (updated query agent)
    def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute method for compatibility with BaseAgent"""
        # This method is not used in the current design
        # The analyze_query method is the main interface
<<<<<<< HEAD
        raise NotImplementedError("Use analyze_query method instead of execute")
=======
        raise NotImplementedError("Use analyze_query method instead of execute") 
>>>>>>> 21f7b0b1 (refactored products agent)
