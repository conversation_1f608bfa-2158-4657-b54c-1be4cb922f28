import time
from abc import ABC, abstractmethod
from typing import Dict, Any, List


class ProductTool(ABC):
    """Base class for all product-related tools."""

    def __init__(self, name: str, description: str, agent_instance=None):
        self.name = name
        self.description = description
        self.agent = agent_instance
        self.execution_count = 0
        self.total_execution_time = 0.0

    @abstractmethod
    def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the tool with given parameters."""
        pass

    def get_schema(self) -> Dict[str, Any]:
        """Return the tool's parameter schema."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self._get_parameters_schema()
        }

    @abstractmethod
    def _get_parameters_schema(self) -> Dict[str, Any]:
        """Return the parameters schema for this tool."""
        pass

    def _log_execution(self, input_params: dict, output: dict, execution_time: float):
        """Log tool execution for debugging and analytics."""
        self.execution_count += 1
        self.total_execution_time += execution_time

        if self.agent and hasattr(self.agent, '_log_tool_usage'):
            self.agent._log_tool_usage(self.name, input_params, output, execution_time)
<<<<<<< HEAD
=======


class BasicProductSearchTool(ProductTool):
    """Tool for basic product search using fuzzy matching."""

    def __init__(self, agent_instance):
        super().__init__(
            name="basic_product_search",
            description="Search for products using fuzzy matching and tag-based filtering. Returns basic product information.",
            agent_instance=agent_instance
        )

    def execute(self, query: str, category: str = None, max_results: int = 5) -> Dict[str, Any]:
        """Execute basic product search."""
        start_time = time.time()

        try:
            results = self._search_products_basic(query, category, max_results)

            output = {
                "success": True,
                "results": results,
                "count": len(results),
                "query": query,
                "category": category
            }

        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "results": [],
                "count": 0
            }

        execution_time = time.time() - start_time
        self._log_execution({"query": query, "category": category}, output, execution_time)

        return output

    def _search_products_basic(self, query: str, category: str = None, max_results: int = 5) -> List[Dict]:
        """Basic product search using existing fuzzy matching logic."""
        products = []
        
        # Use existing search logic from the agent
        search_terms = [query.lower()] + self.agent._generate_synonyms(query, "system")
        tag_matched_products = []

        for product in self.agent.products:
            if category and product.get('category', '').lower() != category.lower():
                continue
            tags = self.agent._get_relevant_tags(product)
            if any(self.agent._tag_match(term, product, tags) for term in search_terms):
                tag_matched_products.append(product)

        # If no tag matches, fallback to all products
        candidates = tag_matched_products if tag_matched_products else [
            p for p in self.agent.products
            if not category or p.get('category', '').lower() == category.lower()
        ]

        # Fuzzy matching and scoring
        scored_products = []
        for product in candidates:
            tags = self.agent._get_relevant_tags(product)
            score = max(self.agent._fuzzy_match(term, product, tags) for term in search_terms)
            if score > 0.1:
                scored_products.append((product, score))

        scored_products.sort(key=lambda x: x[1], reverse=True)
        return [product for product, score in scored_products[:max_results]]

    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Search query from user"},
                "category": {"type": "string", "description": "Optional category filter"},
                "max_results": {"type": "integer", "description": "Maximum number of results", "default": 5}
            },
            "required": ["query"]
        }


class DetailedProductSearchTool(ProductTool):
    """Tool for detailed product search with comprehensive information."""

    def __init__(self, agent_instance):
        super().__init__(
            name="detailed_product_search",
            description="Search for products with comprehensive details and specifications.",
            agent_instance=agent_instance
        )

    def execute(self, query: str, detail_level: str = "detailed") -> Dict[str, Any]:
        """Execute detailed product search."""
        start_time = time.time()

        try:
            results = self._search_products_detailed(query, detail_level)

            output = {
                "success": True,
                "results": results,
                "count": len(results),
                "detail_level": detail_level
            }

        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "results": []
            }

        execution_time = time.time() - start_time
        self._log_execution({"query": query, "detail_level": detail_level}, output, execution_time)

        return output

    def _search_products_detailed(self, query: str, detail_level: str = "detailed") -> List[Dict]:
        """Detailed product search with independent logic."""
        # Independent detailed search logic - don't call basic search
        search_terms = [query.lower()] + self.agent._generate_synonyms(query, "system")
        
        # More comprehensive matching for detailed search
        scored_products = []
        for product in self.agent.products:
            tags = self.agent._get_relevant_tags(product)
            
            # Enhanced scoring for detailed search
            name_score = self.agent._fuzzy_match(query.lower(), product, ['name'])
            desc_score = self.agent._fuzzy_match(query.lower(), product, ['description'])
            tag_score = max(self.agent._fuzzy_match(term, product, tags) for term in search_terms)
            
            # Weighted combined score for detailed search
            combined_score = (name_score * 0.4) + (desc_score * 0.3) + (tag_score * 0.3)
            
            if combined_score > 0.2:  # Higher threshold for detailed search
                scored_products.append((product, combined_score))

        scored_products.sort(key=lambda x: x[1], reverse=True)
        
        # Format results based on detail level
        detailed_results = []
        for product, score in scored_products[:5]:
            if detail_level == "basic":
                result = {
                    "id": product.get('id'),
                    "name": product.get('name'),
                    "price": product.get('price'),
                    "category": product.get('category'),
                    "confidence": round(score, 2)
                }
            elif detail_level == "detailed":
                tags = self.agent._get_relevant_tags(product)
                result = {tag: product.get(tag, None) for tag in tags}
                result["confidence"] = round(score, 2)
            else:  # full
                result = product.copy()
                result["confidence"] = round(score, 2)
            
            detailed_results.append(result)

        return detailed_results

    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Search query from user"},
                "detail_level": {"type": "string", "enum": ["basic", "detailed", "full"], "default": "detailed"}
            },
            "required": ["query"]
        }


class ProductByIdTool(ProductTool):
    """Tool for retrieving specific product by ID."""

    def __init__(self, agent_instance):
        super().__init__(
            name="get_product_by_id",
            description="Retrieve specific product information by product ID.",
            agent_instance=agent_instance
        )

    def execute(self, product_id: str, detail_level: str = "full") -> Dict[str, Any]:
        """Get product by ID."""
        start_time = time.time()

        try:
            product = self._get_product_by_id(product_id, detail_level)

            output = {
                "success": True,
                "product": product,
                "product_id": product_id
            }

        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "product": None
            }

        execution_time = time.time() - start_time
        self._log_execution({"product_id": product_id, "detail_level": detail_level}, output, execution_time)

        return output

    def _get_product_by_id(self, product_id: str, detail_level: str = "full") -> Dict[str, Any]:
        """Get product by ID with specified detail level."""
        for product in self.agent.products:
            if product.get('id') == product_id:
                if detail_level == "basic":
                    return {
                        "id": product.get('id'),
                        "name": product.get('name'),
                        "price": product.get('price'),
                        "category": product.get('category')
                    }
                elif detail_level == "detailed":
                    tags = self.agent._get_relevant_tags(product)
                    return {tag: product.get(tag, None) for tag in tags}
                else:  # full
                    return product
        
        raise ValueError(f"Product with ID {product_id} not found")

    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "product_id": {"type": "string", "description": "Unique product identifier"},
                "detail_level": {"type": "string", "enum": ["basic", "detailed", "full"], "default": "full"}
            },
            "required": ["product_id"]
        }
>>>>>>> 21f7b0b1 (refactored products agent)
